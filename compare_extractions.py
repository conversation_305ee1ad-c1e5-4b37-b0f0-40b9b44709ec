#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档提取结果对比分析工具
对比Python-docx和PowerShell两种提取方法的结果差异
"""

import os
import re
from datetime import datetime

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Error reading {file_path}: {str(e)}"

def clean_text_for_comparison(text):
    """清理文本用于对比分析"""
    # 移除时间戳和文件路径等变化的内容
    text = re.sub(r'提取时间: [\d\-: ]+', '', text)
    text = re.sub(r'Extraction time: [\d\-: ]+', '', text)
    text = re.sub(r'原始文件: [^\n]+', '', text)
    text = re.sub(r'Original file: [^\n]+', '', text)
    text = re.sub(r'文件大小: [^\n]+', '', text)
    text = re.sub(r'File size: [^\n]+', '', text)
    
    # 统一空格和换行
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    return text

def analyze_content_differences(python_content, powershell_content):
    """分析内容差异"""
    
    # 清理内容用于对比
    python_clean = clean_text_for_comparison(python_content)
    powershell_clean = clean_text_for_comparison(powershell_content)
    
    # 基本统计
    python_chars = len(python_clean)
    powershell_chars = len(powershell_clean)
    
    # 分词统计
    python_words = python_clean.split()
    powershell_words = powershell_clean.split()
    
    # 查找差异
    python_set = set(python_words)
    powershell_set = set(powershell_words)
    
    only_in_python = python_set - powershell_set
    only_in_powershell = powershell_set - python_set
    common_words = python_set & powershell_set
    
    # 关键词分析
    key_terms = ['Digital Token', '认证', '安全', '银行', '用户', '系统', '令牌', '移动', 'OTP', 
                 '管理', 'APP', 'SDK', '密钥', '生物识别', '国密', 'HSM', 'API', '合规']
    
    python_key_counts = {}
    powershell_key_counts = {}
    
    for term in key_terms:
        python_key_counts[term] = python_content.count(term)
        powershell_key_counts[term] = powershell_content.count(term)
    
    return {
        'python_chars': python_chars,
        'powershell_chars': powershell_chars,
        'python_words': len(python_words),
        'powershell_words': len(powershell_words),
        'only_in_python': only_in_python,
        'only_in_powershell': only_in_powershell,
        'common_words': len(common_words),
        'python_key_counts': python_key_counts,
        'powershell_key_counts': powershell_key_counts
    }

def check_content_completeness(python_content, powershell_content):
    """检查内容完整性"""
    
    # 检查重要章节是否都存在
    important_sections = [
        '产品概述与背景',
        '目标用户画像',
        '用户故事',
        '核心功能需求',
        '安全需求',
        '技术架构设计',
        '多平台适配',
        '相关标准规范'
    ]
    
    python_sections = {}
    powershell_sections = {}
    
    for section in important_sections:
        python_sections[section] = section in python_content
        powershell_sections[section] = section in powershell_content
    
    # 检查技术细节
    technical_details = [
        'iOS Keychain',
        'Android Keystore',
        'HUKS',
        'Flutter',
        'TOTP',
        'HOTP',
        'SM2/SM3/SM4',
        'RFC 6238',
        'GM/T 0028',
        'JR/T 0068'
    ]
    
    python_tech = {}
    powershell_tech = {}
    
    for detail in technical_details:
        python_tech[detail] = detail in python_content
        powershell_tech[detail] = detail in powershell_content
    
    return {
        'python_sections': python_sections,
        'powershell_sections': powershell_sections,
        'python_tech': python_tech,
        'powershell_tech': powershell_tech
    }

def generate_comparison_report(differences, completeness):
    """生成对比报告"""
    
    report = []
    report.append("=== Word文档提取方法对比分析报告 ===")
    report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 基本统计对比
    report.append("📊 基本统计对比:")
    report.append(f"   Python-docx提取字符数: {differences['python_chars']:,}")
    report.append(f"   PowerShell提取字符数: {differences['powershell_chars']:,}")
    report.append(f"   字符数差异: {abs(differences['python_chars'] - differences['powershell_chars']):,}")
    report.append("")
    report.append(f"   Python-docx词汇数: {differences['python_words']:,}")
    report.append(f"   PowerShell词汇数: {differences['powershell_words']:,}")
    report.append(f"   共同词汇数: {differences['common_words']:,}")
    report.append("")
    
    # 关键词频率对比
    report.append("🔍 关键词频率对比:")
    for term in differences['python_key_counts']:
        py_count = differences['python_key_counts'][term]
        ps_count = differences['powershell_key_counts'][term]
        diff = abs(py_count - ps_count)
        status = "✅" if diff <= 2 else "⚠️" if diff <= 5 else "❌"
        report.append(f"   {status} {term}: Python({py_count}) vs PowerShell({ps_count}) [差异:{diff}]")
    report.append("")
    
    # 章节完整性对比
    report.append("📋 重要章节完整性对比:")
    for section in completeness['python_sections']:
        py_has = completeness['python_sections'][section]
        ps_has = completeness['powershell_sections'][section]
        status = "✅" if py_has and ps_has else "⚠️" if py_has or ps_has else "❌"
        report.append(f"   {status} {section}: Python({py_has}) vs PowerShell({ps_has})")
    report.append("")
    
    # 技术细节完整性对比
    report.append("🔧 技术细节完整性对比:")
    for detail in completeness['python_tech']:
        py_has = completeness['python_tech'][detail]
        ps_has = completeness['powershell_tech'][detail]
        status = "✅" if py_has and ps_has else "⚠️" if py_has or ps_has else "❌"
        report.append(f"   {status} {detail}: Python({py_has}) vs PowerShell({ps_has})")
    report.append("")
    
    # 差异分析
    if differences['only_in_python']:
        report.append("🔍 仅在Python提取中出现的内容:")
        unique_python = list(differences['only_in_python'])[:20]  # 显示前20个
        for item in unique_python:
            if len(item) > 3:  # 过滤掉太短的词
                report.append(f"   • {item}")
        report.append("")
    
    if differences['only_in_powershell']:
        report.append("🔍 仅在PowerShell提取中出现的内容:")
        unique_powershell = list(differences['only_in_powershell'])[:20]  # 显示前20个
        for item in unique_powershell:
            if len(item) > 3:  # 过滤掉太短的词
                report.append(f"   • {item}")
        report.append("")
    
    # 结论
    report.append("📋 对比结论:")
    
    char_diff_percent = abs(differences['python_chars'] - differences['powershell_chars']) / max(differences['python_chars'], differences['powershell_chars']) * 100
    
    if char_diff_percent < 5:
        report.append("   ✅ 两种提取方法的内容基本一致")
    elif char_diff_percent < 15:
        report.append("   ⚠️ 两种提取方法存在一定差异，建议进一步检查")
    else:
        report.append("   ❌ 两种提取方法差异较大，需要详细分析")
    
    # 推荐
    report.append("")
    report.append("💡 推荐使用方案:")
    if differences['python_chars'] > differences['powershell_chars']:
        report.append("   推荐使用Python-docx方法，提取内容更完整")
    elif differences['powershell_chars'] > differences['python_chars']:
        report.append("   推荐使用PowerShell方法，提取内容更完整")
    else:
        report.append("   两种方法提取内容相当，可根据环境选择")
    
    return '\n'.join(report)

def main():
    """主函数"""
    
    # 文件路径
    python_file = 'Digital_Token_PRD_content_20250829_154322.txt'
    powershell_file = 'Digital_Token_PRD_extracted.txt'
    
    print("🔄 开始对比分析Word文档提取结果...")
    
    # 检查文件是否存在
    if not os.path.exists(python_file):
        print(f"❌ Python提取文件不存在: {python_file}")
        return
    
    if not os.path.exists(powershell_file):
        print(f"❌ PowerShell提取文件不存在: {powershell_file}")
        return
    
    # 读取文件内容
    print("📖 读取文件内容...")
    python_content = read_file_content(python_file)
    powershell_content = read_file_content(powershell_file)
    
    if python_content.startswith('Error') or powershell_content.startswith('Error'):
        print("❌ 文件读取失败")
        return
    
    # 分析差异
    print("🔍 分析内容差异...")
    differences = analyze_content_differences(python_content, powershell_content)
    
    # 检查完整性
    print("📋 检查内容完整性...")
    completeness = check_content_completeness(python_content, powershell_content)
    
    # 生成报告
    print("📊 生成对比报告...")
    report = generate_comparison_report(differences, completeness)
    
    # 显示报告
    print("\n" + report)
    
    # 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'Word_Extraction_Comparison_{timestamp}.txt'
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n✅ 对比报告已保存到: {report_file}")
    except Exception as e:
        print(f"\n❌ 保存报告失败: {e}")
    
    print("\n🎉 对比分析完成!")

if __name__ == '__main__':
    main()
