#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片内容分析工具 (无需OCR依赖)
分析从Word文档中提取的图片的基本属性和可能的内容类型
"""

import os
import glob
from datetime import datetime
from PIL import Image
import hashlib

def analyze_image_properties(image_path):
    """
    分析图片的基本属性
    """
    try:
        with Image.open(image_path) as img:
            # 基本属性
            width, height = img.size
            mode = img.mode
            format_type = img.format
            
            # 文件大小
            file_size = os.path.getsize(image_path)
            
            # 计算图片哈希值用于去重
            with open(image_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            # 分析图片特征
            aspect_ratio = width / height
            is_square = abs(aspect_ratio - 1.0) < 0.1
            is_wide = aspect_ratio > 1.5
            is_tall = aspect_ratio < 0.67
            
            # 根据尺寸推测可能的内容类型
            possible_types = []
            
            # 手机截图特征 (常见比例)
            if 0.4 < aspect_ratio < 0.6:  # 手机屏幕比例
                possible_types.append('mobile_screenshot')
            
            # 流程图特征 (通常较宽)
            if aspect_ratio > 1.2 and width > 400:
                possible_types.append('flowchart')
            
            # 架构图特征 (通常较大且接近方形)
            if width > 500 and height > 400 and 0.7 < aspect_ratio < 1.4:
                possible_types.append('architecture_diagram')
            
            # 小图标或按钮
            if width < 200 and height < 200:
                possible_types.append('icon_or_button')
            
            # 表格截图 (通常较宽)
            if aspect_ratio > 1.5 and height < 600:
                possible_types.append('table_or_chart')
            
            # 如果没有特定特征，标记为一般图片
            if not possible_types:
                possible_types.append('general_image')
            
            return {
                'filename': os.path.basename(image_path),
                'path': image_path,
                'width': width,
                'height': height,
                'mode': mode,
                'format': format_type,
                'file_size': file_size,
                'file_hash': file_hash,
                'aspect_ratio': aspect_ratio,
                'is_square': is_square,
                'is_wide': is_wide,
                'is_tall': is_tall,
                'possible_types': possible_types,
                'analysis_success': True
            }
            
    except Exception as e:
        return {
            'filename': os.path.basename(image_path),
            'path': image_path,
            'error': str(e),
            'analysis_success': False
        }

def detect_duplicate_images(image_results):
    """
    检测重复图片
    """
    hash_groups = {}
    for result in image_results:
        if result.get('analysis_success', False):
            file_hash = result['file_hash']
            if file_hash not in hash_groups:
                hash_groups[file_hash] = []
            hash_groups[file_hash].append(result)
    
    duplicates = {hash_val: files for hash_val, files in hash_groups.items() if len(files) > 1}
    return duplicates

def classify_by_size_and_ratio(image_results):
    """
    根据尺寸和比例分类图片
    """
    categories = {
        'large_images': [],      # 大图 (>800px)
        'medium_images': [],     # 中图 (200-800px)
        'small_images': [],      # 小图 (<200px)
        'mobile_screenshots': [],# 手机截图
        'wide_images': [],       # 宽图 (比例>1.5)
        'tall_images': [],       # 高图 (比例<0.67)
        'square_images': []      # 方图 (比例接近1)
    }
    
    for result in image_results:
        if not result.get('analysis_success', False):
            continue
        
        width = result['width']
        height = result['height']
        max_dimension = max(width, height)
        
        # 按尺寸分类
        if max_dimension > 800:
            categories['large_images'].append(result)
        elif max_dimension > 200:
            categories['medium_images'].append(result)
        else:
            categories['small_images'].append(result)
        
        # 按比例分类
        if 'mobile_screenshot' in result['possible_types']:
            categories['mobile_screenshots'].append(result)
        elif result['is_wide']:
            categories['wide_images'].append(result)
        elif result['is_tall']:
            categories['tall_images'].append(result)
        elif result['is_square']:
            categories['square_images'].append(result)
    
    return categories

def generate_image_analysis_report(image_results, duplicates, categories):
    """
    生成图片分析报告
    """
    report = []
    report.append("=== Word文档图片内容深度分析报告 ===")
    report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 基本统计
    total_images = len(image_results)
    successful_analysis = len([r for r in image_results if r.get('analysis_success', False)])
    total_size = sum(r.get('file_size', 0) for r in image_results if r.get('analysis_success', False))
    
    report.append("📊 图片基本统计:")
    report.append(f"   总图片数量: {total_images}")
    report.append(f"   成功分析: {successful_analysis}")
    report.append(f"   总文件大小: {total_size / 1024 / 1024:.2f} MB")
    report.append(f"   平均文件大小: {total_size / successful_analysis / 1024:.2f} KB")
    report.append("")
    
    # 格式分布
    formats = {}
    for result in image_results:
        if result.get('analysis_success', False):
            fmt = result.get('format', 'Unknown')
            formats[fmt] = formats.get(fmt, 0) + 1
    
    report.append("📋 图片格式分布:")
    for fmt, count in sorted(formats.items()):
        report.append(f"   {fmt}: {count}个")
    report.append("")
    
    # 尺寸分类统计
    report.append("📏 图片尺寸分类:")
    for category, images in categories.items():
        if images:
            category_names = {
                'large_images': '大图片 (>800px)',
                'medium_images': '中等图片 (200-800px)',
                'small_images': '小图片 (<200px)',
                'mobile_screenshots': '手机截图',
                'wide_images': '宽图片 (宽高比>1.5)',
                'tall_images': '高图片 (宽高比<0.67)',
                'square_images': '方形图片 (宽高比≈1)'
            }
            name = category_names.get(category, category)
            report.append(f"   {name}: {len(images)}个")
    report.append("")
    
    # 可能的内容类型统计
    type_counts = {}
    for result in image_results:
        if result.get('analysis_success', False):
            for ptype in result.get('possible_types', []):
                type_counts[ptype] = type_counts.get(ptype, 0) + 1
    
    report.append("🔍 推测的内容类型:")
    type_names = {
        'mobile_screenshot': '手机界面截图',
        'flowchart': '流程图',
        'architecture_diagram': '架构图',
        'icon_or_button': '图标或按钮',
        'table_or_chart': '表格或图表',
        'general_image': '一般图片'
    }
    
    for ptype, count in sorted(type_counts.items()):
        name = type_names.get(ptype, ptype)
        report.append(f"   {name}: {count}个")
    report.append("")
    
    # 重复图片检测
    if duplicates:
        report.append("🔄 重复图片检测:")
        for i, (hash_val, files) in enumerate(duplicates.items(), 1):
            report.append(f"   重复组 {i} (共{len(files)}个文件):")
            for file_info in files:
                report.append(f"     • {file_info['filename']}")
        report.append("")
    else:
        report.append("✅ 未发现重复图片")
        report.append("")
    
    # 详细图片信息
    report.append("📸 详细图片信息:")
    for i, result in enumerate(image_results, 1):
        report.append(f"\n--- 图片 {i}: {result['filename']} ---")
        
        if not result.get('analysis_success', False):
            report.append(f"❌ 分析失败: {result.get('error', '未知错误')}")
            continue
        
        # 基本信息
        report.append(f"尺寸: {result['width']} x {result['height']} 像素")
        report.append(f"格式: {result['format']} ({result['mode']})")
        report.append(f"文件大小: {result['file_size'] / 1024:.2f} KB")
        report.append(f"宽高比: {result['aspect_ratio']:.2f}")
        
        # 可能的内容类型
        types_str = ', '.join([type_names.get(t, t) for t in result['possible_types']])
        report.append(f"推测类型: {types_str}")
        
        # 特征标记
        features = []
        if result['is_square']:
            features.append('方形')
        if result['is_wide']:
            features.append('宽图')
        if result['is_tall']:
            features.append('高图')
        
        if features:
            report.append(f"图片特征: {', '.join(features)}")
    
    # 分析建议
    report.append("\n" + "="*60)
    report.append("💡 图片内容分析建议:")
    report.append("")
    
    # 根据分析结果给出具体建议
    mobile_count = type_counts.get('mobile_screenshot', 0)
    flowchart_count = type_counts.get('flowchart', 0)
    architecture_count = type_counts.get('architecture_diagram', 0)
    
    if mobile_count > 0:
        report.append(f"📱 发现 {mobile_count} 个疑似手机界面截图:")
        report.append("   • 这些可能是产品原型图或用户界面设计")
        report.append("   • 建议结合PRD中的用户故事进行分析")
        report.append("   • 可以补充到用户体验设计章节")
        report.append("")
    
    if flowchart_count > 0:
        report.append(f"📊 发现 {flowchart_count} 个疑似流程图:")
        report.append("   • 这些可能包含重要的业务流程信息")
        report.append("   • 建议人工审查并转换为文字描述")
        report.append("   • 可能包含启用流程、认证流程等关键信息")
        report.append("")
    
    if architecture_count > 0:
        report.append(f"🏗️ 发现 {architecture_count} 个疑似架构图:")
        report.append("   • 这些可能是系统架构或技术架构图")
        report.append("   • 建议技术人员详细审查")
        report.append("   • 可能包含关键的技术实现细节")
        report.append("")
    
    report.append("🔧 推荐的进一步分析工具:")
    report.append("   1. 使用OCR工具识别图片中的文字 (Tesseract, PaddleOCR)")
    report.append("   2. 使用AI视觉API分析图片内容 (Azure Vision, Google Vision)")
    report.append("   3. 手动审查重要的业务流程图和架构图")
    report.append("   4. 将关键图片内容转换为文字描述补充到PRD中")
    
    return '\n'.join(report)

def main():
    """主函数"""
    images_dir = 'extracted_images'
    
    if not os.path.exists(images_dir):
        print(f"❌ 图片目录不存在: {images_dir}")
        print("请先运行 extract_images_from_docx.py 提取图片")
        return
    
    print("🔄 开始深度分析图片内容...")
    
    # 获取所有图片文件
    image_patterns = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
    image_files = []
    
    for pattern in image_patterns:
        image_files.extend(glob.glob(os.path.join(images_dir, pattern)))
    
    if not image_files:
        print(f"❌ 在 {images_dir} 目录中未找到图片文件")
        return
    
    print(f"📸 找到 {len(image_files)} 个图片文件")
    
    # 分析每个图片
    image_results = []
    for i, image_path in enumerate(image_files, 1):
        print(f"🔍 分析图片 {i}/{len(image_files)}: {os.path.basename(image_path)}")
        result = analyze_image_properties(image_path)
        image_results.append(result)
    
    # 检测重复图片
    print("🔄 检测重复图片...")
    duplicates = detect_duplicate_images(image_results)
    
    # 分类图片
    print("📊 分类图片...")
    categories = classify_by_size_and_ratio(image_results)
    
    # 生成报告
    print("📋 生成分析报告...")
    report = generate_image_analysis_report(image_results, duplicates, categories)
    
    # 显示简要统计
    successful_count = len([r for r in image_results if r.get('analysis_success', False)])
    mobile_count = sum(1 for r in image_results 
                      if r.get('analysis_success', False) and 'mobile_screenshot' in r.get('possible_types', []))
    
    print(f"\n📊 图片分析完成:")
    print(f"   总图片数: {len(image_results)}")
    print(f"   成功分析: {successful_count}")
    print(f"   疑似手机截图: {mobile_count}")
    print(f"   重复图片组: {len(duplicates)}")
    
    # 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'Image_Content_Analysis_{timestamp}.txt'
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ 图片分析报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    print("\n🎉 图片内容深度分析完成!")

if __name__ == '__main__':
    main()
