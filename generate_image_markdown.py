#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从两个文件夹中按文件名顺序生成图片清单的 Markdown 文件，便于复制到 Word。
"""

import os
import re
import glob
from datetime import datetime
from typing import List


def natural_key(s: str):
    """用于自然排序的 key，例如 '1 (10).jpg' 会排在 '1 (2).jpg' 之后。"""
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]


def collect_images(directory: str) -> List[str]:
    """收集目录中的所有图片文件并按文件名自然排序。"""
    if not os.path.isdir(directory):
        return []
    
    image_patterns = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
    files: List[str] = []
    
    for pattern in image_patterns:
        files.extend(glob.glob(os.path.join(directory, pattern)))
    
    # 按文件名自然排序
    files.sort(key=lambda p: natural_key(os.path.basename(p)))
    return files


def get_image_info(image_path: str) -> dict:
    """获取图片基本信息"""
    try:
        from PIL import Image
        with Image.open(image_path) as img:
            width, height = img.size
            format_type = img.format
            file_size = os.path.getsize(image_path)
            return {
                'width': width,
                'height': height,
                'format': format_type,
                'size_kb': round(file_size / 1024, 2)
            }
    except Exception:
        file_size = os.path.getsize(image_path)
        return {
            'width': '未知',
            'height': '未知', 
            'format': '未知',
            'size_kb': round(file_size / 1024, 2)
        }


def generate_markdown(directories: List[str], output_file: str):
    """生成包含图片信息的 Markdown 文件"""
    
    lines = [
        '# 图片内容汇总',
        '',
        f'- 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
        '- 说明: 本文档按文件夹和文件名顺序整理图片，便于复制到 Word 文档',
        ''
    ]
    
    total_images = 0
    
    # 处理每个目录
    for directory in directories:
        if not os.path.isdir(directory):
            lines.extend([f'## {directory}', '', '（目录不存在）', ''])
            continue
            
        images = collect_images(directory)
        total_images += len(images)
        
        lines.extend([f'## {directory}', ''])
        
        if not images:
            lines.extend(['（无图片文件）', ''])
            continue
        
        # 处理每张图片
        for i, image_path in enumerate(images, 1):
            filename = os.path.basename(image_path)
            info = get_image_info(image_path)
            
            lines.extend([
                f'### {filename}',
                '',
                f'**图片信息:** {info["width"]}×{info["height"]} | {info["format"]} | {info["size_kb"]}KB',
                '',
                '**图片内容:**',
                '',
                '（此处可添加图片描述或OCR识别的文字内容）',
                '',
                '---',
                ''
            ])
    
    # 添加统计信息
    lines.extend([
        '## 统计信息',
        '',
        f'- 总文件夹数: {len(directories)}',
        f'- 总图片数: {total_images}',
        f'- 处理完成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
        ''
    ])
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        print(f'✅ 已生成 Markdown 文件: {output_file}')
        print(f'📊 统计信息: 共处理 {total_images} 张图片')
        return True
    except Exception as e:
        print(f'❌ 生成文件失败: {e}')
        return False


def main():
    """主函数"""
    directories = ['old', 'new']
    output_file = 'images_summary.md'
    
    print('🔄 开始生成图片汇总 Markdown...')
    
    # 检查目录
    existing_dirs = [d for d in directories if os.path.isdir(d)]
    if not existing_dirs:
        print('❌ 未找到指定的图片目录')
        return
    
    print(f'📁 找到目录: {", ".join(existing_dirs)}')
    
    # 生成 Markdown
    if generate_markdown(directories, output_file):
        print(f'🎉 完成！请查看文件: {output_file}')
        print('💡 提示: 该文件可直接在 Word 中打开或复制内容到 Word')
    else:
        print('❌ 生成失败')


if __name__ == '__main__':
    main() 