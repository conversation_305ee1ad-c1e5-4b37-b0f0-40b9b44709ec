#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从两个文件夹中按文件名顺序提取图片中的文字内容（OCR），生成 Markdown 文件，便于复制到 Word。
参考现有 ocr_image_analysis.py 的 OCR 调用方式。
"""

import os
import re
import sys
import glob
from datetime import datetime
from typing import List, Dict, Tuple, Callable, Optional

from PIL import Image
import pytesseract

# 如需手动指定 Tesseract 路径（Windows），取消下一行注释并修改为本机路径
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

IMAGE_PATTERNS = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']


def natural_key(s: str):
    """用于自然排序的 key，例如 '1 (10).jpg' 会排在 '1 (2).jpg' 之后。"""
    return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', s)]


def collect_images(directory: str) -> List[str]:
    """收集目录中的所有图片文件并按文件名自然排序。"""
    if not os.path.isdir(directory):
        return []
    files: List[str] = []
    for pattern in IMAGE_PATTERNS:
        files.extend(glob.glob(os.path.join(directory, pattern)))
    # 按文件名自然排序
    files.sort(key=lambda p: natural_key(os.path.basename(p)))
    return files


def ocr_image_tesseract(image_path: str) -> Tuple[str, str]:
    """使用 Tesseract 对单张图片执行 OCR，返回 (语言, 文本)。失败时返回 ('unknown', 错误信息)。"""
    try:
        image = Image.open(image_path)
        # 中英文混合与英文分别尝试
        try:
            text_chi_eng = pytesseract.image_to_string(image, lang='chi_sim+eng')
        except Exception:
            text_chi_eng = ''
        try:
            text_eng = pytesseract.image_to_string(image, lang='eng')
        except Exception:
            text_eng = ''
        # 选择更长的结果作为更优
        chosen = text_chi_eng if len(text_chi_eng.strip()) >= len(text_eng.strip()) else text_eng
        lang_used = 'chi_sim+eng' if chosen == text_chi_eng else 'eng'
        return lang_used, chosen.strip()
    except Exception as e:
        return 'unknown', f'OCR失败: {e}'


def build_markdown(results: Dict[str, List[Tuple[str, str, str]]], backend_note: str) -> str:
    """
    根据结果构建 Markdown 文本。
    results: { folder: [(filename, lang, text), ...] }
    backend_note: OCR 后端说明
    """
    lines: List[str] = []
    lines.append('# 图片OCR文本汇总')
    lines.append('')
    lines.append(f'- 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    lines.append(f'- 提取方式: {backend_note}')
    lines.append('')

    # 每个文件夹一个二级标题
    for folder in sorted(results.keys(), key=natural_key):
        lines.append(f'## {folder}')
        lines.append('')
        items = results[folder]
        if not items:
            lines.append('（无图片）')
            lines.append('')
            continue
        # 每张图片一个三级标题
        for filename, lang, text in items:
            display_name = filename
            lines.append(f'### {display_name}')
            lines.append('')
            if text:
                lines.append(f'识别语言: {lang}')
                lines.append('')
                # 直接输出纯文本，避免 Markdown 代码块，便于复制到 Word
                lines.append(text)
            else:
                lines.append(f'识别语言: {lang}')
                lines.append('')
                lines.append('（无可识别文字或为空）')
            lines.append('')
    return '\n'.join(lines)


def _try_set_tesseract_cmd() -> bool:
    """在 Windows 上尝试自动设置 Tesseract 可执行文件路径。"""
    # 环境变量优先
    env_path = os.environ.get('TESSERACT_CMD') or os.environ.get('TESSERACT_PATH')
    candidate_paths: List[str] = []
    if env_path:
        candidate_paths.append(env_path)
    # 常见安装路径
    candidate_paths.extend([
        r'C:\\Program Files\\Tesseract-OCR\\tesseract.exe',
        r'C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe',
    ])
    for p in candidate_paths:
        if p and os.path.isfile(p):
            pytesseract.pytesseract.tesseract_cmd = p
            try:
                pytesseract.get_tesseract_version()
                return True
            except Exception:
                pass
    return False


def ensure_tesseract_ready() -> Tuple[bool, str]:
    """确保 Tesseract 可用，返回 (是否可用, 说明)。"""
    try:
        pytesseract.get_tesseract_version()
        return True, 'PATH'
    except Exception as e:
        # 尝试自动设置典型路径
        if _try_set_tesseract_cmd():
            return True, 'AUTO_PATH'
        return False, f'未检测到 Tesseract: {e}'


def ensure_easyocr_ready() -> Tuple[bool, Optional[object], str]:
    """尝试加载 EasyOCR，返回 (是否可用, reader, 说明)。"""
    try:
        import easyocr  # type: ignore
        # 加载中英模型（首次会下载模型）
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        return True, reader, 'EasyOCR (ch_sim+en)'
    except Exception as e:
        return False, None, f'未能加载 EasyOCR: {e}'


def main(argv: List[str]) -> int:
    import argparse
    parser = argparse.ArgumentParser(description='对两个文件夹中的图片进行OCR并导出为Markdown')
    parser.add_argument('--dirs', nargs='+', default=['old', 'new'], help='要处理的图片目录（默认: old new）')
    parser.add_argument('--output', default='images_ocr.md', help='输出Markdown文件名（默认: images_ocr.md）')
    args = parser.parse_args(argv)

    # 选择 OCR 后端
    backend_note = ''
    ocr_func: Optional[Callable[[str], Tuple[str, str]]] = None

    ok, info = ensure_tesseract_ready()
    if ok:
        backend_note = 'Tesseract OCR (chi_sim+eng/eng)'
        ocr_func = ocr_image_tesseract
    else:
        ok2, easy_reader, easy_info = ensure_easyocr_ready()
        if ok2:
            backend_note = easy_info
            def _ocr_image_easyocr(image_path: str) -> Tuple[str, str]:
                try:
                    # 返回纯文本列表
                    import easyocr  # type: ignore
                    lines = easy_reader.readtext(image_path, detail=0)
                    # 合并为多行文本
                    text = '\n'.join([line.strip() for line in lines if isinstance(line, str)])
                    return 'ch_sim+en', text.strip()
                except Exception as e:
                    return 'unknown', f'OCR失败: {e}'
            ocr_func = _ocr_image_easyocr
        else:
            print('❌ 无可用的 OCR 引擎。')
            print('请安装其中之一:')
            print('  1) Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki (并配置 PATH 或设置 tesseract_cmd)')
            print('  2) EasyOCR: pip install easyocr (首次运行会下载模型)')
            return 2

    # 收集目录并 OCR
    results: Dict[str, List[Tuple[str, str, str]]] = {}
    for directory in args.dirs:
        images = collect_images(directory)
        items: List[Tuple[str, str, str]] = []
        for image_path in images:
            lang, text = ocr_func(image_path) if ocr_func else ('unknown', '')
            items.append((os.path.basename(image_path), lang, text))
        results[directory] = items

    # 生成 Markdown
    md_text = build_markdown(results, backend_note)

    # 保存
    try:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(md_text)
        print(f'✅ 已生成 Markdown 文件: {args.output}')
    except Exception as e:
        print(f'❌ 保存失败: {e}')
        return 3

    return 0


if __name__ == '__main__':
    sys.exit(main(sys.argv[1:])) 