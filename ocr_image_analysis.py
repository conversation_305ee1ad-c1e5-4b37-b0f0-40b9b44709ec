#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR图片文字识别工具
用于识别从Word文档中提取的图片中的文字内容
"""

import os
import glob
from datetime import datetime
from PIL import Image
import pytesseract

# 配置Tesseract路径 (Windows)
# 如果Tesseract未安装，请先安装: https://github.com/UB-Mannheim/tesseract/wiki
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def analyze_image_with_ocr(image_path):
    """
    使用OCR分析单个图片
    """
    try:
        # 打开图片
        image = Image.open(image_path)
        
        # 获取图片基本信息
        width, height = image.size
        mode = image.mode
        
        # 尝试OCR识别 (中英文)
        try:
            # 中英文混合识别
            text_chi_eng = pytesseract.image_to_string(image, lang='chi_sim+eng')
            
            # 仅英文识别
            text_eng = pytesseract.image_to_string(image, lang='eng')
            
            # 选择识别结果更好的版本
            if len(text_chi_eng.strip()) > len(text_eng.strip()):
                recognized_text = text_chi_eng
                lang_used = 'chi_sim+eng'
            else:
                recognized_text = text_eng
                lang_used = 'eng'
                
        except Exception as ocr_error:
            recognized_text = f"OCR识别失败: {str(ocr_error)}"
            lang_used = 'unknown'
        
        return {
            'filename': os.path.basename(image_path),
            'path': image_path,
            'width': width,
            'height': height,
            'mode': mode,
            'text': recognized_text.strip(),
            'lang_used': lang_used,
            'text_length': len(recognized_text.strip()),
            'has_text': len(recognized_text.strip()) > 10  # 认为超过10个字符才算有文字
        }
        
    except Exception as e:
        return {
            'filename': os.path.basename(image_path),
            'path': image_path,
            'error': str(e),
            'has_text': False
        }

def classify_image_content(ocr_result):
    """
    根据OCR结果分类图片内容
    """
    if 'error' in ocr_result:
        return 'error'
    
    text = ocr_result.get('text', '').lower()
    
    # 流程图关键词
    flowchart_keywords = ['步骤', 'step', '流程', 'process', '开始', 'start', '结束', 'end', 
                         '确认', 'confirm', '输入', 'input', '登录', 'login']
    
    # 架构图关键词
    architecture_keywords = ['架构', 'architecture', '系统', 'system', '服务', 'service',
                           'api', '数据库', 'database', '服务器', 'server']
    
    # 界面截图关键词
    ui_keywords = ['app', '应用', '界面', 'ui', '按钮', 'button', '菜单', 'menu',
                  '手机', 'mobile', '银行', 'bank']
    
    # 表格图表关键词
    chart_keywords = ['表格', 'table', '图表', 'chart', '数据', 'data', '统计', 'statistics']
    
    if any(keyword in text for keyword in flowchart_keywords):
        return 'flowchart'
    elif any(keyword in text for keyword in architecture_keywords):
        return 'architecture'
    elif any(keyword in text for keyword in ui_keywords):
        return 'ui_screenshot'
    elif any(keyword in text for keyword in chart_keywords):
        return 'chart_table'
    elif ocr_result.get('has_text', False):
        return 'text_content'
    else:
        return 'image_only'

def generate_ocr_analysis_report(ocr_results):
    """
    生成OCR分析报告
    """
    report = []
    report.append("=== Word文档图片OCR分析报告 ===")
    report.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 统计信息
    total_images = len(ocr_results)
    successful_ocr = len([r for r in ocr_results if 'error' not in r])
    has_text_count = len([r for r in ocr_results if r.get('has_text', False)])
    
    report.append("📊 OCR分析统计:")
    report.append(f"   总图片数: {total_images}")
    report.append(f"   成功识别: {successful_ocr}")
    report.append(f"   包含文字: {has_text_count}")
    report.append(f"   识别成功率: {successful_ocr/total_images*100:.1f}%")
    report.append("")
    
    # 按类型分类
    categories = {}
    for result in ocr_results:
        category = classify_image_content(result)
        categories[category] = categories.get(category, 0) + 1
    
    report.append("📋 图片内容分类:")
    category_names = {
        'flowchart': '流程图',
        'architecture': '架构图',
        'ui_screenshot': '界面截图',
        'chart_table': '图表表格',
        'text_content': '文字内容',
        'image_only': '纯图片',
        'error': '识别失败'
    }
    
    for category, count in categories.items():
        name = category_names.get(category, category)
        report.append(f"   {name}: {count}个")
    report.append("")
    
    # 详细OCR结果
    report.append("🔍 详细OCR识别结果:")
    for i, result in enumerate(ocr_results, 1):
        report.append(f"\n--- 图片 {i}: {result['filename']} ---")
        
        if 'error' in result:
            report.append(f"❌ 处理失败: {result['error']}")
            continue
        
        # 基本信息
        report.append(f"尺寸: {result['width']}x{result['height']} ({result['mode']})")
        report.append(f"识别语言: {result['lang_used']}")
        report.append(f"文字长度: {result['text_length']}字符")
        report.append(f"内容分类: {category_names.get(classify_image_content(result), '未知')}")
        
        # OCR识别的文字内容
        if result.get('has_text', False):
            text_preview = result['text'][:300]  # 显示前300字符
            if len(result['text']) > 300:
                text_preview += "...(内容已截断)"
            report.append(f"识别文字:\n{text_preview}")
        else:
            report.append("识别文字: (无文字内容或识别失败)")
    
    report.append("\n" + "="*60)
    report.append("💡 分析建议:")
    report.append("1. 对于流程图和架构图，建议人工审查确认关键业务流程")
    report.append("2. 对于界面截图，可以补充到用户体验分析中")
    report.append("3. 对于识别失败的图片，建议手动查看重要性")
    report.append("4. 重要的技术架构图建议转换为文字描述")
    
    return '\n'.join(report)

def main():
    """主函数"""
    images_dir = 'extracted_images'
    
    if not os.path.exists(images_dir):
        print(f"❌ 图片目录不存在: {images_dir}")
        print("请先运行 extract_images_from_docx.py 提取图片")
        return
    
    # 检查Tesseract是否可用
    try:
        pytesseract.get_tesseract_version()
        print("✅ Tesseract OCR 可用")
    except Exception as e:
        print(f"❌ Tesseract OCR 不可用: {e}")
        print("请安装 Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki")
        print("或者安装 pytesseract: pip install pytesseract")
        return
    
    print("🔄 开始OCR分析图片内容...")
    
    # 获取所有图片文件
    image_patterns = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
    image_files = []
    
    for pattern in image_patterns:
        image_files.extend(glob.glob(os.path.join(images_dir, pattern)))
    
    if not image_files:
        print(f"❌ 在 {images_dir} 目录中未找到图片文件")
        return
    
    print(f"📸 找到 {len(image_files)} 个图片文件")
    
    # OCR分析每个图片
    ocr_results = []
    for i, image_path in enumerate(image_files, 1):
        print(f"🔍 分析图片 {i}/{len(image_files)}: {os.path.basename(image_path)}")
        result = analyze_image_with_ocr(image_path)
        ocr_results.append(result)
    
    # 生成报告
    print("📊 生成OCR分析报告...")
    report = generate_ocr_analysis_report(ocr_results)
    
    # 显示简要统计
    successful_count = len([r for r in ocr_results if 'error' not in r])
    text_count = len([r for r in ocr_results if r.get('has_text', False)])
    
    print(f"\n📊 OCR分析完成:")
    print(f"   总图片数: {len(ocr_results)}")
    print(f"   成功识别: {successful_count}")
    print(f"   包含文字: {text_count}")
    
    # 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'OCR_Analysis_Report_{timestamp}.txt'
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"✅ OCR分析报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    print("\n🎉 OCR图片分析完成!")

if __name__ == '__main__':
    main()
